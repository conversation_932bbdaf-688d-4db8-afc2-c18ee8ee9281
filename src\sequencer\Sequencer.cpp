/**
 * @brief Map a normalized value in the range [0.0, 1.0] to the specific parameter's value range.
 *
 * This helper function converts a normalized floating-point input into the actual parameter value,
 * based on the parameter's defined minimum and maximum limits. It supports parameters with
 * underlying types of float, int, or bool.
 *
 * @param id The identifier of the parameter (ParamId enum).
 * @param normalizedValue A float value normalized between 0.0 and 1.0.
 * @return The mapped parameter value as a float within the parameter's valid range.
 *
 * @note Clamping and rounding are handled separately by the ParameterManager's setValue method.
 */
float mapNormalizedValueToParamRange(ParamId id, float normalizedValue)
{
    const auto &def = CORE_PARAMETERS[static_cast<size_t>(id)];

    // Safely get float from the variant, regardless of underlying type (int, float, bool)
    auto get_float = [](const ParameterValueType &v) -> float
    {
        return std::visit(
            [](auto &&arg) -> float
            {
                using T = std::decay_t<decltype(arg)>;
                if constexpr (std::is_same_v<T, float>) return arg;
                if constexpr (std::is_same_v<T, int>) return static_cast<float>(arg);
                if constexpr (std::is_same_v<T, bool>) return arg ? 1.0f : 0.0f;
                return 0.0f; // Fallback
            },
            v);
    };

    float minVal = get_float(def.minValue);
    float maxVal = get_float(def.maxValue);

    // The ParameterManager's setValue will handle clamping and rounding.
    return minVal + normalizedValue * (maxVal - minVal);
}
